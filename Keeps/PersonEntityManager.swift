//
//  PersonEntityManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import Foundation
import CoreData
import SwiftUI

/// Manages the bridge between Person class and PersonEntity Core Data model
/// Provides relationship intelligence and enhanced tracking capabilities
class PersonEntityManager: ObservableObject {
    
    // MARK: - Properties
    
    private let context: NSManagedObjectContext
    
    @Published var relationshipInsights: [UUID: RelationshipInsight] = [:]
    @Published var interactionHistory: [UUID: [Interaction]] = [:]
    @Published var relationshipGoals: [UUID: [RelationshipGoal]] = [:]
    
    // MARK: - Initialization
    
    init(context: NSManagedObjectContext) {
        self.context = context
    }
    
    // MARK: - Core Data Bridge Methods
    
    /// Convert Person to PersonEntity
    func createPersonEntity(from person: Person) -> PersonEntity {
        let entity = PersonEntity(context: context)
        updatePersonEntity(entity, from: person)
        return entity
    }
    
    /// Update PersonEntity from Person
    func updatePersonEntity(_ entity: PersonEntity, from person: Person) {
        // Basic Information
        entity.id = person.id
        entity.name = person.name
        entity.role = person.role.isEmpty ? nil : person.role
        entity.company = person.company.isEmpty ? nil : person.company
        entity.email = person.email.isEmpty ? nil : person.email
        entity.phone = person.phone.isEmpty ? nil : person.phone
        entity.avatarImageName = person.avatarImageName
        entity.location = person.location.isEmpty ? nil : person.location
        entity.timezone = person.timezone
        entity.birthday = person.birthday
        
        // Status & Availability
        // Note: Removed isOnline, lastSeen, and availability tracking
        entity.isFavorite = person.isFavorite
        
        // Relationship Intelligence
        entity.relationshipType = person.relationshipType.rawValue
        entity.interactionFrequency = person.interactionFrequency.rawValue
        entity.relationshipStrength = calculateRelationshipStrength(for: person)
        entity.lastInteractionDate = getLastInteractionDate(for: person.id)
        entity.totalInteractions = Int32(getTotalInteractions(for: person.id))
        
        // Notes & Content
        entity.notes = person.notes.isEmpty ? nil : person.notes
        entity.notesData = person.notesData
        
        // Tags and Social Links (stored as JSON)
        if !person.tags.isEmpty {
            entity.tags = encodeToJSON(person.tags)
        }
        if !person.socialLinks.isEmpty {
            entity.socialLinks = encodeToJSON(person.socialLinks)
        }
    }
    
    /// Convert PersonEntity to Person
    func createPerson(from entity: PersonEntity) -> Person {
        let person = Person(
            name: entity.name ?? "Unknown",
            role: entity.role ?? "",
            company: entity.company ?? "",
            email: entity.email ?? "",
            phone: entity.phone ?? "",
            avatarImageName: entity.avatarImageName ?? "person.circle.fill",
            relationshipType: Person.RelationshipType(rawValue: entity.relationshipType ?? "other") ?? .other,
            interactionFrequency: Person.InteractionFrequency(rawValue: entity.interactionFrequency ?? "rarely") ?? .rarely,
            location: entity.location ?? "",
            timezone: entity.timezone ?? "UTC",
            isFavorite: entity.isFavorite
        )
        
        // Set additional properties
        // Note: Removed lastSeen tracking
        person.notes = entity.notes ?? ""
        person.notesData = entity.notesData
        person.birthday = entity.birthday
        
        // Decode tags and social links
        if let tagsJSON = entity.tags {
            person.tags = decodeFromJSON([String].self, from: tagsJSON) ?? []
        }
        if let socialLinksJSON = entity.socialLinks {
            person.socialLinks = decodeFromJSON([Person.SocialLink].self, from: socialLinksJSON) ?? []
        }
        
        return person
    }
    
    // MARK: - Relationship Intelligence Methods
    
    /// Calculate relationship strength based on multiple factors
    func calculateRelationshipStrength(for person: Person) -> Double {
        var strength: Double = 0.0
        
        // Base strength from interaction frequency (using priority value)
        let frequencyValue = Double(person.interactionFrequency.priority)
        strength += frequencyValue * 0.1
        
        // Boost for favorites
        if person.isFavorite {
            strength += 0.2
        }
        
        // Boost for recent interactions
        let daysSinceLastInteraction = daysSince(getLastInteractionDate(for: person.id))
        if daysSinceLastInteraction <= 7 {
            strength += 0.2
        } else if daysSinceLastInteraction <= 30 {
            strength += 0.1
        }
        
        // Boost for complete profile
        let completeness = calculateProfileCompleteness(for: person)
        strength += completeness * 0.2
        
        return min(1.0, strength)
    }
    
    /// Calculate profile completeness
    func calculateProfileCompleteness(for person: Person) -> Double {
        var completeness: Double = 0.0
        let totalFields: Double = 8.0
        
        if !person.name.isEmpty { completeness += 1.0 }
        if !person.role.isEmpty { completeness += 1.0 }
        if !person.company.isEmpty { completeness += 1.0 }
        if !person.email.isEmpty { completeness += 1.0 }
        if !person.phone.isEmpty { completeness += 1.0 }
        if !person.location.isEmpty { completeness += 1.0 }
        if person.birthday != nil { completeness += 1.0 }
        if !person.notes.isEmpty { completeness += 1.0 }
        
        return completeness / totalFields
    }
    
    // MARK: - Interaction Tracking
    
    /// Add new interaction
    func addInteraction(for personId: UUID, type: InteractionType, content: String, date: Date = Date()) {
        let interaction = Interaction(
            id: UUID(),
            personId: personId,
            type: type,
            content: content,
            date: date
        )
        
        if interactionHistory[personId] == nil {
            interactionHistory[personId] = []
        }
        interactionHistory[personId]?.append(interaction)
        
        // Update last interaction date in Core Data
        updateLastInteractionDate(for: personId, date: date)
    }
    
    /// Get last interaction date
    func getLastInteractionDate(for personId: UUID) -> Date? {
        return interactionHistory[personId]?.last?.date
    }
    
    /// Get total interactions count
    func getTotalInteractions(for personId: UUID) -> Int {
        return interactionHistory[personId]?.count ?? 0
    }
    
    // MARK: - Relationship Goals
    
    /// Add relationship goal
    func addRelationshipGoal(for personId: UUID, title: String, description: String, targetDate: Date? = nil) {
        let goal = RelationshipGoal(
            id: UUID(),
            personId: personId,
            title: title,
            description: description,
            targetDate: targetDate,
            isCompleted: false,
            createdDate: Date()
        )
        
        if relationshipGoals[personId] == nil {
            relationshipGoals[personId] = []
        }
        relationshipGoals[personId]?.append(goal)
    }
    
    /// Complete relationship goal
    func completeRelationshipGoal(goalId: UUID) {
        for (personId, goals) in relationshipGoals {
            if let index = goals.firstIndex(where: { $0.id == goalId }) {
                relationshipGoals[personId]?[index].isCompleted = true
                relationshipGoals[personId]?[index].completedDate = Date()
                break
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func encodeToJSON<T: Codable>(_ object: T) -> String? {
        guard let data = try? JSONEncoder().encode(object) else { return nil }
        return String(data: data, encoding: .utf8)
    }
    
    private func decodeFromJSON<T: Codable>(_ type: T.Type, from json: String) -> T? {
        guard let data = json.data(using: .utf8) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
    
    private func daysSince(_ date: Date?) -> Int {
        guard let date = date else { return Int.max }
        return Calendar.current.dateComponents([.day], from: date, to: Date()).day ?? Int.max
    }
    
    private func updateLastInteractionDate(for personId: UUID, date: Date) {
        let request: NSFetchRequest<PersonEntity> = PersonEntity.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", personId as CVarArg)
        
        do {
            let entities = try context.fetch(request)
            if let entity = entities.first {
                entity.lastInteractionDate = date
                entity.totalInteractions += 1
                try context.save()
            }
        } catch {
            print("Error updating last interaction date: \(error)")
        }
    }
}

// MARK: - Supporting Models

/// Represents an interaction with a person
struct Interaction: Identifiable, Codable {
    let id: UUID
    let personId: UUID
    let type: InteractionType
    let content: String
    let date: Date
    var duration: TimeInterval?
    var location: String?
    var mood: String?
    var outcome: String?
    var followUpRequired: Bool = false
    var followUpDate: Date?
    var tags: [String] = []
}

/// Types of interactions
enum InteractionType: String, CaseIterable, Codable {
    case meeting = "Meeting"
    case call = "Call"
    case email = "Email"
    case message = "Message"
    case coffee = "Coffee"
    case lunch = "Lunch"
    case event = "Event"
    case project = "Project"
    case other = "Other"
    
    var icon: String {
        switch self {
        case .meeting: return "person.2.fill"
        case .call: return "phone.fill"
        case .email: return "envelope.fill"
        case .message: return "message.fill"
        case .coffee: return "cup.and.saucer.fill"
        case .lunch: return "fork.knife"
        case .event: return "calendar"
        case .project: return "folder.fill"
        case .other: return "ellipsis.circle.fill"
        }
    }
}

/// Represents a relationship goal
struct RelationshipGoal: Identifiable, Codable {
    let id: UUID
    let personId: UUID
    let title: String
    let description: String
    let targetDate: Date?
    var isCompleted: Bool
    let createdDate: Date
    var completedDate: Date?
}

/// Relationship insights and analytics
struct RelationshipInsight: Identifiable {
    let id = UUID()
    let personId: UUID
    let strength: Double
    let trend: TrendDirection
    let lastInteraction: Date?
    let interactionFrequency: Double
    let recommendations: [String]
    
    enum TrendDirection {
        case improving, stable, declining
    }
}
