//
//  SimpleAddPersonView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI

/// Clean, simplified view for adding a new person
/// Follows Critique2's recommendations for streamlined design
struct SimpleAddPersonView: View {
    let peopleManager: PeopleManager
    @Environment(\.dismiss) private var dismiss
    
    // Essential fields
    @State private var name = ""
    @State private var role = ""
    @State private var company = ""
    @State private var email = ""
    @State private var phone = ""
    @State private var relationshipType: Person.RelationshipType = .colleague
    @State private var interactionFrequency: Person.InteractionFrequency = .rarely
    @State private var isFavorite = false
    
    // Optional fields (expandable section)
    @State private var location = ""
    @State private var notes = ""
    @State private var showOptionalFields = false
    
    // UI state
    @State private var isCreating = false
    @FocusState private var focusedField: Field?
    
    enum Field {
        case name, role, company, email, phone, location, notes
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Add Contact")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Add a new person to your contacts")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top)
                    
                    // Essential fields
                    VStack(spacing: 16) {
                        // Name (required)
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Name")
                                    .font(.headline)
                                Text("*")
                                    .foregroundColor(.red)
                            }
                            
                            TextField("Enter full name", text: $name)
                                .textFieldStyle(CleanTextFieldStyle())
                                .focused($focusedField, equals: .name)
                        }
                        
                        // Role
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Role")
                                .font(.headline)
                            
                            TextField("Job title or role", text: $role)
                                .textFieldStyle(CleanTextFieldStyle())
                                .focused($focusedField, equals: .role)
                        }
                        
                        // Company
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Company")
                                .font(.headline)
                            
                            TextField("Company or organization", text: $company)
                                .textFieldStyle(CleanTextFieldStyle())
                                .focused($focusedField, equals: .company)
                        }
                        
                        // Contact info
                        HStack(spacing: 12) {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Email")
                                    .font(.headline)
                                
                                TextField("<EMAIL>", text: $email)
                                    .textFieldStyle(CleanTextFieldStyle())
                                    .keyboardType(.emailAddress)
                                    .autocapitalization(.none)
                                    .focused($focusedField, equals: .email)
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Phone")
                                    .font(.headline)
                                
                                TextField("Phone number", text: $phone)
                                    .textFieldStyle(CleanTextFieldStyle())
                                    .keyboardType(.phonePad)
                                    .focused($focusedField, equals: .phone)
                            }
                        }
                        
                        // Relationship type
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Relationship")
                                .font(.headline)
                            
                            Picker("Relationship Type", selection: $relationshipType) {
                                ForEach(Person.RelationshipType.allCases, id: \.self) { type in
                                    Text(type.rawValue).tag(type)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                        }
                        
                        // Contact frequency
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Contact Frequency")
                                .font(.headline)
                            
                            Picker("Interaction Frequency", selection: $interactionFrequency) {
                                ForEach(Person.InteractionFrequency.allCases, id: \.self) { frequency in
                                    Text(frequency.description).tag(frequency)
                                }
                            }
                            .pickerStyle(MenuPickerStyle())
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        
                        // Favorite toggle
                        HStack {
                            Text("Mark as Favorite")
                                .font(.headline)
                            
                            Spacer()
                            
                            Toggle("", isOn: $isFavorite)
                                .toggleStyle(SwitchToggleStyle(tint: .yellow))
                        }
                    }
                    
                    // Optional fields section
                    VStack(spacing: 16) {
                        Button(action: { 
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showOptionalFields.toggle()
                            }
                        }) {
                            HStack {
                                Text("Optional Details")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: showOptionalFields ? "chevron.up" : "chevron.down")
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        if showOptionalFields {
                            VStack(spacing: 16) {
                                // Location
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Location")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    TextField("City, Country", text: $location)
                                        .textFieldStyle(CleanTextFieldStyle())
                                        .focused($focusedField, equals: .location)
                                }
                                
                                // Notes
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Notes")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    TextField("Additional notes...", text: $notes, axis: .vertical)
                                        .textFieldStyle(CleanTextFieldStyle())
                                        .lineLimit(3...6)
                                        .focused($focusedField, equals: .notes)
                                }
                            }
                            .transition(.opacity.combined(with: .move(edge: .top)))
                        }
                    }
                    
                    // Action buttons
                    VStack(spacing: 12) {
                        Button(action: addPerson) {
                            HStack {
                                if isCreating {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "person.badge.plus")
                                }
                                
                                Text(isCreating ? "Adding..." : "Add Contact")
                                    .fontWeight(.semibold)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(name.isEmpty ? Color.gray : Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        .disabled(name.isEmpty || isCreating)
                        
                        Button("Cancel") {
                            dismiss()
                        }
                        .foregroundColor(.secondary)
                    }
                }
                .padding()
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            focusedField = .name
        }
    }
    
    private func addPerson() {
        guard !name.isEmpty else { return }
        
        isCreating = true
        
        let newPerson = Person(
            name: name,
            role: role,
            company: company,
            email: email,
            phone: phone,
            relationshipType: relationshipType,
            interactionFrequency: interactionFrequency,
            location: location,
            isFavorite: isFavorite
        )
        
        if !notes.isEmpty {
            newPerson.notes = notes
        }
        
        peopleManager.addPerson(newPerson)
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // Dismiss with delay for smooth animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            dismiss()
        }
    }
}

/// Clean text field style
struct CleanTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
    }
}

#Preview {
    SimpleAddPersonView(peopleManager: PeopleManager())
}
