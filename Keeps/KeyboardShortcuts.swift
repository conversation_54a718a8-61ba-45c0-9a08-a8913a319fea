//
//  KeyboardShortcuts.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 30/05/2025.
//

import SwiftUI

// MARK: - Keyboard Shortcuts Support

/// Enhanced keyboard shortcuts for power users
struct KeyboardShortcutsModifier: ViewModifier {
    @ObservedObject var peopleManager: PeopleManager
    @Binding var showingSearch: Bo<PERSON>
    @Binding var showingFilters: Bo<PERSON>
    @Binding var showingAddPerson: Bool
    
    func body(content: Content) -> some View {
        content
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.keyCommandNotification)) { notification in
                handleKeyCommand(notification)
            }
            // Note: SwiftUI keyboard shortcuts are limited on iOS
            // For full keyboard support, we'd need to implement UIKeyCommand
    }
    
    private func handleKeyCommand(_ notification: Notification) {
        // Handle key commands
        guard let keyCommand = notification.object as? UIKeyCommand else { return }

        switch keyCommand.input {
        case "f":
            if keyCommand.modifierFlags.contains(.command) {
                // Cmd+F: Focus search
                withAnimation(.easeInOut(duration: 0.3)) {
                    showingSearch = true
                }
            }
        case "n":
            if keyCommand.modifierFlags.contains(.command) {
                // Cmd+N: New contact
                showingAddPerson = true
            }
        case "1", "2", "3":
            // View mode shortcuts removed - simplified interface uses single grid view
            break
        case "r":
            if keyCommand.modifierFlags.contains([.command, .shift]) {
                // Cmd+Shift+R: Clear filters
                withAnimation(.easeInOut(duration: 0.3)) {
                    peopleManager.clearFilters()
                    showingSearch = false
                    showingFilters = false
                }
            }
        case "t":
            if keyCommand.modifierFlags.contains(.command) {
                // Cmd+T: Toggle filters
                withAnimation(.easeInOut(duration: 0.3)) {
                    showingFilters.toggle()
                }
            }
        case UIKeyCommand.inputEscape:
            // Escape: Close search/filters
            withAnimation(.easeInOut(duration: 0.3)) {
                if showingSearch {
                    showingSearch = false
                    peopleManager.searchText = ""
                } else if showingFilters {
                    showingFilters = false
                }
            }
        case "g":
            if keyCommand.modifierFlags.contains(.command) {
                // Cmd+G: Find next (if in search mode)
                if showingSearch && !peopleManager.searchText.isEmpty {
                    // Implement find next functionality
                }
            }
        case "d":
            if keyCommand.modifierFlags.contains(.command) {
                // Cmd+D: Duplicate contact (if one is selected)
                if peopleManager.selectedPerson != nil {
                    // Implement duplicate functionality
                }
            }
        default:
            break
        }
    }
}

/// Extension to add keyboard shortcuts to any view
extension View {
    func keyboardShortcuts(
        peopleManager: PeopleManager,
        showingSearch: Binding<Bool>,
        showingFilters: Binding<Bool>,
        showingAddPerson: Binding<Bool>
    ) -> some View {
        self.modifier(
            KeyboardShortcutsModifier(
                peopleManager: peopleManager,
                showingSearch: showingSearch,
                showingFilters: showingFilters,
                showingAddPerson: showingAddPerson
            )
        )
    }
}

// MARK: - Keyboard Shortcuts Help

/// Keyboard shortcuts help overlay
struct KeyboardShortcutsHelp: View {
    @Binding var isPresented: Bool
    
    private let shortcuts: [(String, String, String)] = [
        ("⌘F", "Search", "Focus search field"),
        ("⌘N", "New Contact", "Add new contact"),
        ("⌘1", "Cards View", "Switch to cards view"),
        ("⌘2", "List View", "Switch to list view"),
        ("⌘3", "Grid View", "Switch to grid view"),
        ("⌘T", "Toggle Filters", "Show/hide filters"),
        ("⌘⇧R", "Clear Filters", "Reset all filters"),
        ("⌘G", "Find Next", "Find next search result"),
        ("⌘D", "Duplicate", "Duplicate selected contact"),
        ("⎋", "Cancel", "Close search or filters")
    ]
    
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.3)
                .ignoresSafeArea()
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }
            
            // Help content
            VStack(spacing: 20) {
                // Header
                HStack {
                    Text("Keyboard Shortcuts")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isPresented = false
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.secondary)
                    }
                }
                
                // Shortcuts list
                VStack(spacing: 12) {
                    ForEach(shortcuts, id: \.0) { shortcut in
                        HStack {
                            // Key combination
                            Text(shortcut.0)
                                .font(.system(.body, design: .monospaced))
                                .fontWeight(.semibold)
                                .foregroundColor(.blue)
                                .frame(width: 60, alignment: .leading)
                            
                            // Action name
                            Text(shortcut.1)
                                .font(.body)
                                .fontWeight(.medium)
                                .frame(width: 100, alignment: .leading)
                            
                            // Description
                            Text(shortcut.2)
                                .font(.body)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }
                }
                
                // Footer
                Text("Press ⌘? to show this help again")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(24)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.regularMaterial)
                    .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .frame(maxWidth: 500)
        }
    }
}

// MARK: - UIApplication Extension for Key Commands

extension UIApplication {
    static let keyCommandNotification = Notification.Name("KeyCommandNotification")
}

/// Custom key command handling
class KeyCommandHandler: UIResponder {
    override var canBecomeFirstResponder: Bool { true }

    override var keyCommands: [UIKeyCommand]? {
        return [
            // Search and navigation
            UIKeyCommand(input: "f", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: "n", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: "t", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: "r", modifierFlags: [.command, .shift], action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: UIKeyCommand.inputEscape, modifierFlags: [], action: #selector(handleKeyCommand(_:))),

            // View modes
            UIKeyCommand(input: "1", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: "2", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: "3", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),

            // Additional actions
            UIKeyCommand(input: "g", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: "d", modifierFlags: .command, action: #selector(handleKeyCommand(_:))),
            UIKeyCommand(input: "?", modifierFlags: .command, action: #selector(handleKeyCommand(_:)))
        ]
    }

    @objc private func handleKeyCommand(_ command: UIKeyCommand) {
        NotificationCenter.default.post(
            name: UIApplication.keyCommandNotification,
            object: command
        )
    }
}
