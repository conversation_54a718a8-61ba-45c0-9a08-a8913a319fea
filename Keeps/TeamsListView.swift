//
//  TeamsListView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Main view displaying the list of teams with real functionality
/// Now includes search, filtering, team management, and real-time updates
struct TeamsListView: View {
    @StateObject private var teamManager = TeamManager()
    @State private var showingTeamDetail = false
    @State private var showingCreateTeam = false
    @State private var showingSearch = false
    @State private var showingNotifications = false
    

    
    var body: some View {
        NavigationView {
            ZStack {
                // Background color
                Color(.systemGroupedBackground)
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header with search and notifications
                    HeaderView(
                        searchText: $teamManager.searchText,
                        showingSearch: $showingSearch,
                        showingNotifications: $showingNotifications,
                        unreadCount: teamManager.unreadNotificationCount
                    )
                    .padding(.horizontal)
                    .padding(.top, 8)

                    // Filter tabs
                    FilterTabsView(selectedCategory: $teamManager.selectedCategory)
                        .padding(.top, 8)

                    // Enhanced Teams scroll view with SwiftUIX magic
                    CocoaScrollView(.vertical, showsIndicators: false) {
                        LazyVStack(spacing: 20) {
                            // Analytics card with parallax effect
                            AnalyticsCardView(analytics: teamManager.getTeamAnalytics())
                                .padding(.horizontal)
                                .modifier(ParallaxEffectModifier(offset: 0.3))

                            // Team cards with staggered animations and proper tap handling
                            ForEach(Array(teamManager.filteredTeams.enumerated()), id: \.element.id) { index, team in
                                TeamCardView(team: team, teamManager: teamManager)
                                    .modifier(StaggeredAppearanceModifier(index: index))
                                    .contentShape(Rectangle()) // Make entire card tappable
                                    .onTapGesture {
                                        // Haptic feedback
                                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                        impactFeedback.impactOccurred()

                                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                            teamManager.selectedTeam = team
                                            showingTeamDetail = true
                                        }
                                    }
                                    .contextMenu {
                                        TeamContextMenu(team: team, teamManager: teamManager)
                                    }
                                    .scaleEffect(teamManager.selectedTeam?.id == team.id ? 0.98 : 1.0)
                                    .animation(.spring(response: 0.4, dampingFraction: 0.8), value: teamManager.selectedTeam?.id)
                            }
                        }
                        .padding(.top, 20)
                        .padding(.bottom, 120) // Space for floating buttons
                    }
                    .refreshable {
                        await refreshTeams()
                    }
                }

                // Floating action buttons
                VStack {
                    Spacer()
                    FloatingActionButtonsView(
                        teamManager: teamManager,
                        showingCreateTeam: $showingCreateTeam,
                        showingSearch: $showingSearch
                    )
                    .padding(.bottom, 34) // Safe area padding
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingTeamDetail) {
            if let team = teamManager.selectedTeam {
                TeamDetailView(team: team, teamManager: teamManager)
            }
        }
        .sheet(isPresented: $showingCreateTeam) {
            CreateTeamView(teamManager: teamManager)
        }
        .sheet(isPresented: $showingNotifications) {
            NotificationsView(teamManager: teamManager)
        }
    }

    // MARK: - Refresh Function
    private func refreshTeams() async {
        // Simulate network refresh with haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        try? await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 seconds

        // Refresh team data
        teamManager.loadSampleData()

        // Success haptic
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
    }
}

// MARK: - SwiftUIX Enhanced Modifiers

/// Parallax effect modifier for enhanced scrolling
struct ParallaxEffectModifier: ViewModifier {
    let offset: CGFloat

    func body(content: Content) -> some View {
        content
            .modifier(ScrollOffsetModifier())
            .transformEffect(.init(translationX: 0, y: offset * 10))
    }
}

/// Card hover effect with subtle animations
struct CardHoverEffect: ViewModifier {
    @State private var isPressed = false

    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .shadow(
                color: .black.opacity(isPressed ? 0.15 : 0.08),
                radius: isPressed ? 8 : 5,
                x: 0,
                y: isPressed ? 4 : 2
            )
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
    }
}

/// Staggered appearance animation for list items
struct StaggeredAppearanceModifier: ViewModifier {
    let index: Int
    @State private var hasAppeared = false

    func body(content: Content) -> some View {
        content
            .opacity(hasAppeared ? 1 : 0)
            .offset(y: hasAppeared ? 0 : 30)
            .animation(
                .spring(response: 0.6, dampingFraction: 0.8)
                .delay(Double(index) * 0.1),
                value: hasAppeared
            )
            .onAppear {
                hasAppeared = true
            }
    }
}

/// Scroll offset tracking modifier
struct ScrollOffsetModifier: ViewModifier {
    @State private var offset: CGFloat = 0

    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .preference(key: ScrollOffsetPreferenceKey.self, value: geometry.frame(in: .named("scroll")).minY)
                }
            )
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                offset = value
            }
    }
}

/// Preference key for scroll offset
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0

    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

/// Filter tabs component for team categories with proper alignment
struct FilterTabsView: View {
    @Binding var selectedCategory: TeamCategory

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(TeamCategory.allCases, id: \.self) { category in
                    FilterTabButton(
                        title: category.displayName,
                        isSelected: selectedCategory == category,
                        action: {
                            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                                selectedCategory = category
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, 20)
        }
    }
}

/// Individual filter tab button
struct FilterTabButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.blue : Color.clear)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.gray.opacity(0.3), lineWidth: isSelected ? 0 : 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Header view with search and notifications
struct HeaderView: View {
    @Binding var searchText: String
    @Binding var showingSearch: Bool
    @Binding var showingNotifications: Bool
    let unreadCount: Int

    var body: some View {
        HStack {
            // App title
            if !showingSearch {
                VStack(alignment: .leading) {
                    Text("Teams")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    Text("Collaborate & Connect")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // Search bar
            if showingSearch {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)

                    TextField("Search teams, members...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())

                    if !searchText.isEmpty {
                        Button("Clear") {
                            searchText = ""
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(.systemGray6))
                .cornerRadius(10)

                Button("Cancel") {
                    showingSearch = false
                    searchText = ""
                }
                .foregroundColor(.blue)
            } else {
                // Search button
                Button(action: {
                    showingSearch = true
                }) {
                    Image(systemName: "magnifyingglass")
                        .font(.title2)
                        .foregroundColor(.primary)
                }

                // Notifications button
                Button(action: {
                    showingNotifications = true
                }) {
                    ZStack {
                        Image(systemName: "bell")
                            .font(.title2)
                            .foregroundColor(.primary)

                        if unreadCount > 0 {
                            Text("\(unreadCount)")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(Circle().fill(Color.red))
                                .offset(x: 10, y: -10)
                        }
                    }
                }
            }
        }
    }
}

/// Analytics card showing team overview
struct AnalyticsCardView: View {
    let analytics: TeamAnalytics

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Team Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text("Live")
                    .font(.caption)
                    .foregroundColor(.green)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
            }

            HStack(spacing: 20) {
                AnalyticsItem(
                    title: "Teams",
                    value: "\(analytics.totalTeams)",
                    subtitle: "\(analytics.activeTeams) active",
                    color: .blue
                )

                AnalyticsItem(
                    title: "Members",
                    value: "\(analytics.totalMembers)",
                    subtitle: "Total members",
                    color: .green
                )

                AnalyticsItem(
                    title: "Events",
                    value: "\(analytics.upcomingEvents)",
                    subtitle: "upcoming",
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}

/// Individual analytics item
struct AnalyticsItem: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)

            Text(subtitle)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

/// Context menu for team cards
struct TeamContextMenu: View {
    let team: Team
    let teamManager: TeamManager

    var body: some View {
        Group {
            Button(action: {
                teamManager.startQuickMeeting(for: team)
            }) {
                Label("Quick Meeting", systemImage: "video")
            }

            Button(action: {
                // Toggle team active status
                team.isActive.toggle()
            }) {
                Label(team.isActive ? "Pause Team" : "Activate Team",
                      systemImage: team.isActive ? "pause" : "play")
            }

            Button(action: {
                teamManager.sendTeamAnnouncement("Team update from mobile", to: team)
            }) {
                Label("Send Announcement", systemImage: "megaphone")
            }

            Divider()

            Button(role: .destructive, action: {
                teamManager.deleteTeam(team)
            }) {
                Label("Delete Team", systemImage: "trash")
            }
        }
    }
}

/// Enhanced floating action buttons with SwiftUIX magic
struct FloatingActionButtonsView: View {
    let teamManager: TeamManager
    @Binding var showingCreateTeam: Bool
    @Binding var showingSearch: Bool
    @State private var isAnimating = false
    @State private var showingQuickActions = false

    var body: some View {
        HStack(spacing: 16) {
            // Quick actions button with morphing animation
            Button(action: {
                // Haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                // Show quick actions menu
                showingQuickActions = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "bolt")
                        .font(.system(size: 16, weight: .medium))
                        .modifier(RotatingEffect(isAnimating: isAnimating))
                    Text("Quick Actions")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(
                            LinearGradient(
                                colors: [Color.blue, Color.blue.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .modifier(FloatingButtonEffect())
                )
            }

            Spacer()

            // Create team button with bounce animation
            Button(action: {
                // Enhanced haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
                impactFeedback.impactOccurred()

                showingCreateTeam = true
            }) {
                Image(systemName: "plus")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color.blue, Color.purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .modifier(FloatingButtonEffect())
                    )
                    .modifier(BounceEffect(isAnimating: isAnimating))
            }
        }
        .padding(.horizontal, 20)
        .shadow(color: .black.opacity(0.2), radius: 15, x: 0, y: 8)
        .onAppear {
            isAnimating = true
        }
        .actionSheet(isPresented: $showingQuickActions) {
            ActionSheet(
                title: Text("Quick Actions"),
                message: Text("Choose an action to perform"),
                buttons: [
                    .default(Text("View Team Analytics")) {
                        // Navigate to analytics
                        print("Navigate to team analytics")
                    },
                    .default(Text("Load Sample Data")) {
                        teamManager.loadSampleData()
                    },
                    .default(Text("Clear Notifications")) {
                        teamManager.clearAllNotifications()
                    },
                    .default(Text("Refresh Teams")) {
                        // Trigger a refresh of team data
                        print("Refreshing team data")
                    },
                    .cancel()
                ]
            )
        }
    }
}



#Preview {
    TeamsListView()
}
