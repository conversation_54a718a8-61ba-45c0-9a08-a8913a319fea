//
//  ProfessionalContactsInterface.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 02/06/2025.
//

import SwiftUI
import SwiftUIX

/// Clean, simplified interface replacing the emotional complexity
/// Provides essential functionality with professional design
struct CleanContactsInterface: View {
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @Binding var showingAddPerson: Bool
    @State private var showingSearch = false
    @State private var showingFilters = false

    var body: some View {
        VStack(spacing: 0) {
            // Clean header
            CleanHeaderView(
                peopleManager: peopleManager,
                showingSearch: $showingSearch,
                showingFilters: $showingFilters
            )
            .padding(.horizontal)
            .padding(.top, 8)
            .padding(.bottom, 12)
            .background(Color(.systemBackground))

            // Filter controls
            if showingFilters {
                CleanFilterView(peopleManager: peopleManager)
                    .transition(.move(edge: .top).combined(with: .opacity))
            }

            // Main content
            ProfessionalContactsInterface(
                peopleManager: peopleManager,
                navigationCoordinator: navigationCoordinator
            )
        }
        .overlay(alignment: .bottomTrailing) {
            // Clean add button
            Button(action: { showingAddPerson = true }) {
                Image(systemName: "plus")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.white)
                    .frame(width: 56, height: 56)
                    .background(Color.blue)
                    .clipShape(Circle())
                    .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
            }
            .padding(.trailing, 20)
            .padding(.bottom, 34)
        }
        .navigationBarHidden(true)
    }
}

/// Clean header without emotional complexity
struct CleanHeaderView: View {
    @ObservedObject var peopleManager: PeopleManager
    @Binding var showingSearch: Bool
    @Binding var showingFilters: Bool

    var body: some View {
        VStack(spacing: 16) {
            // Main header row
            HStack {
                if !showingSearch {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Contacts")
                            .font(.largeTitle)
                            .fontWeight(.bold)

                        Text("\(peopleManager.filteredPeople.count) contacts")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }

                // Search field
                if showingSearch {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)

                        TextField("Search contacts...", text: $peopleManager.searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                    .transition(.move(edge: .trailing).combined(with: .opacity))
                }

                // Control buttons
                HStack(spacing: 12) {
                    // Search toggle
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingSearch.toggle()
                            if !showingSearch {
                                peopleManager.searchText = ""
                            }
                        }
                    }) {
                        Image(systemName: showingSearch ? "xmark" : "magnifyingglass")
                            .font(.system(size: 16))
                            .foregroundColor(.primary)
                            .frame(width: 32, height: 32)
                            .background(Circle().fill(Color(.systemGray6)))
                    }

                    // Filter toggle
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingFilters.toggle()
                        }
                    }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.system(size: 16))
                            .foregroundColor(.primary)
                            .frame(width: 32, height: 32)
                            .background(Circle().fill(Color(.systemGray6)))
                    }
                }
            }
        }
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: showingSearch)
    }
}

/// Clean filter view
struct CleanFilterView: View {
    @ObservedObject var peopleManager: PeopleManager

    var body: some View {
        VStack(spacing: 12) {
            // Relationship type filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    Button("All") {
                        peopleManager.selectedRelationshipType = nil
                    }
                    .buttonStyle(FilterButtonStyle(isSelected: peopleManager.selectedRelationshipType == nil))

                    ForEach(Person.RelationshipType.allCases, id: \.self) { type in
                        Button(type.rawValue) {
                            peopleManager.selectedRelationshipType = type
                        }
                        .buttonStyle(FilterButtonStyle(isSelected: peopleManager.selectedRelationshipType == type))
                    }
                }
                .padding(.horizontal)
            }

            // Quick filters
            HStack {
                Button(action: { peopleManager.showFavoritesOnly.toggle() }) {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                        Text("Favorites")
                    }
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(peopleManager.showFavoritesOnly ? Color.yellow.opacity(0.2) : Color(.systemGray6))
                    .foregroundColor(peopleManager.showFavoritesOnly ? .orange : .primary)
                    .cornerRadius(16)
                }

                Spacer()

                Button("Clear All") {
                    peopleManager.clearFilters()
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 12)
        .background(Color(.systemGroupedBackground))
    }
}

struct FilterButtonStyle: ButtonStyle {
    let isSelected: Bool

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.caption)
            .fontWeight(.medium)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(isSelected ? Color.blue : Color(.systemGray6))
            .foregroundColor(isSelected ? .white : .primary)
            .cornerRadius(16)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

/// Clean, professional interface for managing contacts
/// Replaces the emotional complexity with streamlined functionality
struct ProfessionalContactsInterface: View {
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @State private var viewMode: ViewMode = .list
    
    enum ViewMode: String, CaseIterable {
        case list = "List"
        case grid = "Grid"
        
        var icon: String {
            switch self {
            case .list: return "list.bullet"
            case .grid: return "square.grid.2x2"
            }
        }
    }
    
    var body: some View {
        GeometryReader { geometry in
            if !peopleManager.filteredPeople.isEmpty {
                switch viewMode {
                case .list:
                    ProfessionalListView(
                        people: peopleManager.filteredPeople,
                        peopleManager: peopleManager,
                        navigationCoordinator: navigationCoordinator
                    )
                case .grid:
                    ProfessionalGridView(
                        people: peopleManager.filteredPeople,
                        peopleManager: peopleManager,
                        navigationCoordinator: navigationCoordinator
                    )
                }
            } else {
                ProfessionalEmptyState(
                    hasFilters: !peopleManager.searchText.isEmpty ||
                               peopleManager.selectedRelationshipType != nil ||
                               peopleManager.showFavoritesOnly
                )
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    ForEach(ViewMode.allCases, id: \.self) { mode in
                        Button(action: { viewMode = mode }) {
                            Label(mode.rawValue, systemImage: mode.icon)
                        }
                    }
                } label: {
                    Image(systemName: viewMode.icon)
                        .foregroundColor(.primary)
                }
            }
        }
    }
}

/// Clean list view for contacts
struct ProfessionalListView: View {
    let people: [Person]
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 8) {
                ForEach(Array(people.enumerated()), id: \.element.id) { index, person in
                    CleanPersonCard(
                        person: person,
                        index: index,
                        peopleManager: peopleManager,
                        navigationCoordinator: navigationCoordinator
                    )
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 100)
        }
    }
}

/// Clean grid view for contacts
struct ProfessionalGridView: View {
    let people: [Person]
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    
    private let columns = [
        GridItem(.flexible(), spacing: 12),
        GridItem(.flexible(), spacing: 12)
    ]
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVGrid(columns: columns, spacing: 12) {
                ForEach(Array(people.enumerated()), id: \.element.id) { index, person in
                    CompactPersonCard(
                        person: person,
                        index: index,
                        peopleManager: peopleManager,
                        navigationCoordinator: navigationCoordinator
                    )
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 100)
        }
    }
}

/// Clean, professional person card without emotional complexity
struct CleanPersonCard: View {
    @ObservedObject var person: Person
    let index: Int
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @State private var isPressed = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Avatar
            Circle()
                .fill(person.relationshipType.color.opacity(0.2))
                .frame(width: 50, height: 50)
                .overlay(
                    Circle()
                        .stroke(person.relationshipType.color, lineWidth: 2)
                )
                .overlay(
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(person.relationshipType.color)
                )
            
            // Person details
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(person.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    if person.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                    }
                    
                    Spacer()
                    
                    // Relationship type badge
                    Text(person.relationshipType.rawValue)
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(person.relationshipType.color.opacity(0.1))
                        .foregroundColor(person.relationshipType.color)
                        .cornerRadius(8)
                }
                
                if !person.contactInfo.isEmpty {
                    Text(person.contactInfo)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                // Contact frequency
                Text("Contact: \(person.interactionFrequency.description)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Quick actions
            VStack(spacing: 8) {
                if !person.email.isEmpty {
                    Button(action: { openEmail(person.email) }) {
                        Image(systemName: "envelope")
                            .font(.system(size: 16))
                            .foregroundColor(.blue)
                    }
                }
                
                if !person.phone.isEmpty {
                    Button(action: { openPhone(person.phone) }) {
                        Image(systemName: "phone")
                            .font(.system(size: 16))
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            navigationCoordinator.navigateToPerson(person.id)
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
    
    private func openEmail(_ email: String) {
        if let url = URL(string: "mailto:\(email)") {
            UIApplication.shared.open(url)
        }
    }
    
    private func openPhone(_ phone: String) {
        if let url = URL(string: "tel:\(phone)") {
            UIApplication.shared.open(url)
        }
    }
}

/// Compact card for grid view
struct CompactPersonCard: View {
    @ObservedObject var person: Person
    let index: Int
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @State private var isPressed = false
    
    var body: some View {
        VStack(spacing: 12) {
            // Avatar
            Circle()
                .fill(person.relationshipType.color.opacity(0.2))
                .frame(width: 60, height: 60)
                .overlay(
                    Circle()
                        .stroke(person.relationshipType.color, lineWidth: 2)
                )
                .overlay(
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(person.relationshipType.color)
                )
            
            VStack(spacing: 4) {
                HStack {
                    Text(person.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .lineLimit(1)
                    
                    if person.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.yellow)
                    }
                }
                
                Text(person.relationshipType.rawValue)
                    .font(.caption)
                    .foregroundColor(person.relationshipType.color)
                    .fontWeight(.medium)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            navigationCoordinator.navigateToPerson(person.id)
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
    }
}

/// Clean empty state without emotional language
struct ProfessionalEmptyState: View {
    let hasFilters: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "person.2")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(hasFilters ? "No contacts match your filters" : "No contacts yet")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(hasFilters ? "Try adjusting your search or filters" : "Add your first contact to get started")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
}
