//
//  PeopleListView.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI

/// Clean, professional people management interface
/// Simplified design following Critique2's recommendations
struct PeopleListView: View {
    @StateObject private var peopleManager = PeopleManager()
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @State private var showingAddPerson = false
    @State private var searchText = ""
    @State private var selectedFilter: PersonFilter = .all

    enum PersonFilter: String, CaseIterable {
        case all = "All"
        case favorites = "Favorites"
        case colleagues = "Colleagues"
        case friends = "Friends"
        case family = "Family"

        var icon: String {
            switch self {
            case .all: return "person.3"
            case .favorites: return "star.fill"
            case .colleagues: return "briefcase.fill"
            case .friends: return "heart.fill"
            case .family: return "house.fill"
            }
        }
    }

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Clean search and filter bar
                SearchAndFilterBar(
                    searchText: $searchText,
                    selectedFilter: $selectedFilter,
                    peopleManager: people<PERSON>anager
                )

                // Simple grid of people
                PeopleGrid(
                    peopleManager: peopleManager,
                    navigationCoordinator: navigationCoordinator,
                    searchText: searchText,
                    selectedFilter: selectedFilter
                )
            }
            .navigationTitle("People")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button(action: { showingAddPerson = true }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
            }
        }
        .sheet(isPresented: $showingAddPerson) {
            SimpleAddPersonView(peopleManager: peopleManager)
        }
    }
}

/// Clean search and filter bar
struct SearchAndFilterBar: View {
    @Binding var searchText: String
    @Binding var selectedFilter: PeopleListView.PersonFilter
    @ObservedObject var peopleManager: PeopleManager

    var body: some View {
        VStack(spacing: 12) {
            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)

                TextField("Search people...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())

                if !searchText.isEmpty {
                    Button(action: { searchText = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(10)

            // Filter tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(PeopleListView.PersonFilter.allCases, id: \.self) { filter in
                        FilterTab(
                            filter: filter,
                            isSelected: selectedFilter == filter,
                            count: getFilterCount(filter),
                            action: { selectedFilter = filter }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }

    private func getFilterCount(_ filter: PeopleListView.PersonFilter) -> Int {
        let filteredPeople = peopleManager.people.filter { person in
            // Apply search filter
            let matchesSearch = searchText.isEmpty ||
                person.name.localizedCaseInsensitiveContains(searchText) ||
                person.company.localizedCaseInsensitiveContains(searchText) ||
                person.role.localizedCaseInsensitiveContains(searchText)

            guard matchesSearch else { return false }

            // Apply category filter
            switch filter {
            case .all:
                return true
            case .favorites:
                return person.isFavorite
            case .colleagues:
                return person.relationshipType == .colleague
            case .friends:
                return person.relationshipType == .friend
            case .family:
                return person.relationshipType == .family
            }
        }
        return filteredPeople.count
    }
}

/// Clean filter tab component
struct FilterTab: View {
    let filter: PeopleListView.PersonFilter
    let isSelected: Bool
    let count: Int
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: filter.icon)
                    .font(.caption)

                Text(filter.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)

                if count > 0 {
                    Text("\(count)")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(isSelected ? .white : .secondary)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(isSelected ? Color.white.opacity(0.3) : Color(.systemGray5))
                        )
                }
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? Color.blue : Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// Clean people grid component
struct PeopleGrid: View {
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    let searchText: String
    let selectedFilter: PeopleListView.PersonFilter

    private var filteredPeople: [Person] {
        peopleManager.people.filter { person in
            // Apply search filter
            let matchesSearch = searchText.isEmpty ||
                person.name.localizedCaseInsensitiveContains(searchText) ||
                person.company.localizedCaseInsensitiveContains(searchText) ||
                person.role.localizedCaseInsensitiveContains(searchText)

            guard matchesSearch else { return false }

            // Apply category filter
            switch selectedFilter {
            case .all:
                return true
            case .favorites:
                return person.isFavorite
            case .colleagues:
                return person.relationshipType == .colleague
            case .friends:
                return person.relationshipType == .friend
            case .family:
                return person.relationshipType == .family
            }
        }
    }

    var body: some View {
        ScrollView {
            if filteredPeople.isEmpty {
                PeopleEmptyStateView(filter: selectedFilter, searchText: searchText)
                    .padding(.top, 60)
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 16) {
                    ForEach(filteredPeople) { person in
                        PersonCard(person: person) {
                            navigationCoordinator.navigateToPerson(person.id)
                        }
                    }
                }
                .padding()
            }
        }
    }
}



/// Clean person card component
struct PersonCard: View {
    let person: Person
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // Avatar
                ZStack {
                    Circle()
                        .fill(person.relationshipType.color.opacity(0.1))
                        .frame(width: 60, height: 60)

                    if person.avatarImageName == "person.circle.fill" {
                        Text(person.initials)
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(person.relationshipType.color)
                    } else {
                        Image(systemName: person.avatarImageName)
                            .font(.title2)
                            .foregroundColor(person.relationshipType.color)
                    }

                    // Favorite indicator
                    if person.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                            .background(
                                Circle()
                                    .fill(.white)
                                    .frame(width: 16, height: 16)
                            )
                            .offset(x: 20, y: -20)
                    }
                }

                // Info
                VStack(spacing: 4) {
                    Text(person.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(1)
                        .foregroundColor(.primary)

                    if !person.role.isEmpty {
                        Text(person.role)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }

                    if !person.company.isEmpty {
                        Text(person.company)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }

                // Relationship indicator
                HStack(spacing: 4) {
                    Image(systemName: person.relationshipType.icon)
                        .font(.caption2)
                        .foregroundColor(person.relationshipType.color)

                    Text(person.relationshipType.rawValue)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray5), lineWidth: 0.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}


/// Empty state view for when no people match filters
struct PeopleEmptyStateView: View {
    let filter: PeopleListView.PersonFilter
    let searchText: String

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: emptyStateIcon)
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text(emptyStateTitle)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text(emptyStateMessage)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
        }
        .padding()
    }

    private var emptyStateIcon: String {
        if !searchText.isEmpty {
            return "magnifyingglass"
        }

        switch filter {
        case .all:
            return "person.3"
        case .favorites:
            return "star"
        case .colleagues:
            return "briefcase"
        case .friends:
            return "heart"
        case .family:
            return "house"
        }
    }

    private var emptyStateTitle: String {
        if !searchText.isEmpty {
            return "No Results"
        }

        switch filter {
        case .all:
            return "No People Yet"
        case .favorites:
            return "No Favorites"
        case .colleagues:
            return "No Colleagues"
        case .friends:
            return "No Friends"
        case .family:
            return "No Family"
        }
    }

    private var emptyStateMessage: String {
        if !searchText.isEmpty {
            return "Try adjusting your search or browse all people."
        }

        switch filter {
        case .all:
            return "Add your first contact to get started."
        case .favorites:
            return "Mark people as favorites to see them here."
        case .colleagues:
            return "Add colleagues from work to see them here."
        case .friends:
            return "Add friends to see them here."
        case .family:
            return "Add family members to see them here."
        }
    }
}

#Preview {
    PeopleListView(navigationCoordinator: NavigationCoordinator())
}
