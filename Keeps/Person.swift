//
//  Person.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import Foundation

/// Represents a person/contact with enhanced properties for the floating bubbles design
/// Extends beyond team members to include all personal and professional contacts
class Person: Identifiable, Codable, ObservableObject, Equatable {
    let id = UUID()
    @Published var name: String
    @Published var role: String
    @Published var company: String
    @Published var email: String
    @Published var phone: String
    @Published var avatarImageName: String
    @Published var relationshipType: RelationshipType
    @Published var interactionFrequency: InteractionFrequency
    @Published var notes: String
    @Published var notesData: Data? // For storing individual note entries
    @Published var tags: [String]
    @Published var socialLinks: [SocialLink]
    @Published var location: String
    @Published var timezone: String
    @Published var birthday: Date?
    @Published var isFavorite: Bool
    
    /// Relationship types for organizing contacts
    enum RelationshipType: String, CaseIterable, Codable {
        case colleague = "Colleague"
        case friend = "Friend"
        case family = "Family"
        case client = "Client"
        case vendor = "Vendor"
        case mentor = "Mentor"
        case other = "Other"
        
        var color: Color {
            switch self {
            case .colleague: return .blue
            case .friend: return .green
            case .family: return .red
            case .client: return .purple
            case .vendor: return .orange
            case .mentor: return .yellow
            case .other: return .gray
            }
        }

        /// Professional color palette for modern UI
        var professionalColor: Color {
            switch self {
            case .colleague: return Color(red: 0.2, green: 0.4, blue: 0.8)  // Professional blue
            case .friend: return Color(red: 0.1, green: 0.7, blue: 0.4)     // Emerald green
            case .family: return Color(red: 0.8, green: 0.3, blue: 0.4)     // Warm coral
            case .client: return Color(red: 0.6, green: 0.2, blue: 0.8)     // Deep purple
            case .vendor: return Color(red: 0.9, green: 0.5, blue: 0.1)     // Amber orange
            case .mentor: return Color(red: 0.8, green: 0.6, blue: 0.1)     // Golden yellow
            case .other: return Color(red: 0.5, green: 0.5, blue: 0.6)      // Sophisticated gray
            }
        }
        
        var icon: String {
            switch self {
            case .colleague: return "briefcase"
            case .friend: return "heart"
            case .family: return "house"
            case .client: return "handshake"
            case .vendor: return "cart"
            case .mentor: return "graduationcap"
            case .other: return "person"
            }
        }
    }
    

    
    /// Interaction frequency affects bubble size and positioning
    enum InteractionFrequency: String, CaseIterable, Codable {
        case daily = "Daily"
        case weekly = "Weekly"
        case monthly = "Monthly"
        case rarely = "Rarely"
        case never = "Never"
        
        var bubbleScale: CGFloat {
            switch self {
            case .daily: return 1.3
            case .weekly: return 1.1
            case .monthly: return 1.0
            case .rarely: return 0.9
            case .never: return 0.8
            }
        }
        
        var priority: Int {
            switch self {
            case .daily: return 5
            case .weekly: return 4
            case .monthly: return 3
            case .rarely: return 2
            case .never: return 1
            }
        }

        /// Simple description for UI
        var description: String {
            switch self {
            case .daily: return "Daily"
            case .weekly: return "Weekly"
            case .monthly: return "Monthly"
            case .rarely: return "Rarely"
            case .never: return "Never"
            }
        }
    }
    
    /// Social media links
    struct SocialLink: Codable, Identifiable, Equatable {
        let id = UUID()
        let platform: String
        let url: String
        let username: String

        static func == (lhs: SocialLink, rhs: SocialLink) -> Bool {
            return lhs.id == rhs.id
        }
    }
    
    init(
        name: String,
        role: String = "",
        company: String = "",
        email: String = "",
        phone: String = "",
        avatarImageName: String = "person.circle.fill",
        relationshipType: RelationshipType = .other,
        interactionFrequency: InteractionFrequency = .rarely,
        location: String = "",
        timezone: String = "UTC",
        isFavorite: Bool = false
    ) {
        self.name = name
        self.role = role
        self.company = company
        self.email = email
        self.phone = phone
        self.avatarImageName = avatarImageName
        self.relationshipType = relationshipType
        self.interactionFrequency = interactionFrequency
        self.notes = ""
        self.notesData = nil
        self.tags = []
        self.socialLinks = []
        self.location = location
        self.timezone = timezone
        self.birthday = nil
        self.isFavorite = isFavorite
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case id, name, role, company, email, phone, avatarImageName
        case relationshipType, interactionFrequency, notes, notesData, tags, socialLinks
        case location, timezone, birthday, isFavorite
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        role = try container.decode(String.self, forKey: .role)
        company = try container.decode(String.self, forKey: .company)
        email = try container.decode(String.self, forKey: .email)
        phone = try container.decode(String.self, forKey: .phone)
        avatarImageName = try container.decode(String.self, forKey: .avatarImageName)
        relationshipType = try container.decode(RelationshipType.self, forKey: .relationshipType)
        interactionFrequency = try container.decode(InteractionFrequency.self, forKey: .interactionFrequency)
        notes = try container.decode(String.self, forKey: .notes)
        notesData = try container.decodeIfPresent(Data.self, forKey: .notesData)
        tags = try container.decode([String].self, forKey: .tags)
        socialLinks = try container.decode([SocialLink].self, forKey: .socialLinks)
        location = try container.decode(String.self, forKey: .location)
        timezone = try container.decode(String.self, forKey: .timezone)
        birthday = try container.decodeIfPresent(Date.self, forKey: .birthday)
        isFavorite = try container.decode(Bool.self, forKey: .isFavorite)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(role, forKey: .role)
        try container.encode(company, forKey: .company)
        try container.encode(email, forKey: .email)
        try container.encode(phone, forKey: .phone)
        try container.encode(avatarImageName, forKey: .avatarImageName)
        try container.encode(relationshipType, forKey: .relationshipType)
        try container.encode(interactionFrequency, forKey: .interactionFrequency)
        try container.encode(notes, forKey: .notes)
        try container.encodeIfPresent(notesData, forKey: .notesData)
        try container.encode(tags, forKey: .tags)
        try container.encode(socialLinks, forKey: .socialLinks)
        try container.encode(location, forKey: .location)
        try container.encode(timezone, forKey: .timezone)
        try container.encode(birthday, forKey: .birthday)
        try container.encode(isFavorite, forKey: .isFavorite)
    }
    
    // MARK: - Computed Properties
    
    /// Display name with fallback
    var displayName: String {
        name.isEmpty ? "Unknown" : name
    }
    
    /// Full contact info for display
    var contactInfo: String {
        var info: [String] = []
        if !role.isEmpty { info.append(role) }
        if !company.isEmpty { info.append(company) }
        return info.joined(separator: " at ")
    }

    /// Initials for avatar display
    var initials: String {
        let components = name.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.map { String($0) }
        return initials.prefix(2).joined().uppercased()
    }
    


    // MARK: - Equatable Implementation

    static func == (lhs: Person, rhs: Person) -> Bool {
        return lhs.id == rhs.id
    }
}
