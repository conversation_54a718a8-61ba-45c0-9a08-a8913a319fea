//
//  EmotionalHapticsManager.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import UIKit
import SwiftUI

/// Advanced haptic feedback system that creates emotional connections through touch
/// Provides contextual haptic patterns based on relationship types and interactions
class EmotionalHapticsManager {
    static let shared = EmotionalHapticsManager()
    
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    private let mediumImpact = UIImpactFeedbackGenerator(style: .medium)
    private let heavyImpact = UIImpactFeedbackGenerator(style: .heavy)
    private let selectionFeedback = UISelectionFeedbackGenerator()
    private let notificationFeedback = UINotificationFeedbackGenerator()
    
    private init() {
        // Prepare generators for immediate response
        lightImpact.prepare()
        mediumImpact.prepare()
        heavyImpact.prepare()
        selectionFeedback.prepare()
        notificationFeedback.prepare()
    }
    
    // MARK: - Emotional Haptic Patterns
    
    /// Heartbeat pattern for favorite contacts
    func playHeartbeatPattern() {
        DispatchQueue.main.async { [weak self] in
            // First beat
            self?.mediumImpact.impactOccurred()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                // Second beat (slightly softer)
                self?.lightImpact.impactOccurred()
            }
        }
    }
    
    /// Gentle pulse for close relationships
    func playConnectionPulse(for frequency: Person.InteractionFrequency) {
        let intensity = Double(frequency.priority) / 5.0 // Convert priority (1-5) to intensity (0.2-1.0)
        
        DispatchQueue.main.async { [weak self] in
            if intensity > 0.7 {
                self?.mediumImpact.impactOccurred()
            } else if intensity > 0.4 {
                self?.lightImpact.impactOccurred()
            } else {
                self?.selectionFeedback.selectionChanged()
            }
        }
    }
    
    /// Relationship-based haptic feedback
    func playRelationshipFeedback(for type: Person.RelationshipType) {
        DispatchQueue.main.async { [weak self] in
            switch type {
            case .family:
                // Warm, strong pattern for family
                self?.heavyImpact.impactOccurred()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self?.mediumImpact.impactOccurred()
                }
                
            case .friend:
                // Playful pattern for friends
                self?.lightImpact.impactOccurred()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                    self?.lightImpact.impactOccurred()
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self?.mediumImpact.impactOccurred()
                }
                
            case .colleague:
                // Professional, crisp pattern
                self?.mediumImpact.impactOccurred()
                
            case .client:
                // Formal, structured pattern
                self?.lightImpact.impactOccurred()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    self?.lightImpact.impactOccurred()
                }
                
            case .mentor:
                // Respectful, ascending pattern
                self?.lightImpact.impactOccurred()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self?.mediumImpact.impactOccurred()
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    self?.heavyImpact.impactOccurred()
                }
                
            default:
                self?.selectionFeedback.selectionChanged()
            }
        }
    }
    
    /// Celebration haptics for special moments
    func playCelebrationPattern() {
        DispatchQueue.main.async { [weak self] in
            self?.notificationFeedback.notificationOccurred(.success)
            
            // Follow with a joyful pattern
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self?.lightImpact.impactOccurred()
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                self?.mediumImpact.impactOccurred()
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self?.lightImpact.impactOccurred()
            }
        }
    }
    
    /// Gentle notification for birthdays and anniversaries
    func playSpecialMomentPattern() {
        DispatchQueue.main.async { [weak self] in
            // Soft, warm pattern
            self?.lightImpact.impactOccurred()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self?.lightImpact.impactOccurred()
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                self?.mediumImpact.impactOccurred()
            }
        }
    }
    
    /// Connection established haptic
    func playConnectionEstablished() {
        DispatchQueue.main.async { [weak self] in
            self?.selectionFeedback.selectionChanged()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                self?.lightImpact.impactOccurred()
            }
        }
    }
    
    /// Emotional interaction feedback
    func playEmotionalInteraction(intensity: Double) {
        DispatchQueue.main.async { [weak self] in
            if intensity > 0.8 {
                // Very strong emotional connection
                self?.heavyImpact.impactOccurred()
            } else if intensity > 0.5 {
                // Moderate emotional connection
                self?.mediumImpact.impactOccurred()
            } else {
                // Light emotional connection
                self?.lightImpact.impactOccurred()
            }
        }
    }
    
    /// Breathing pattern for online contacts
    func playBreathingPattern() {
        DispatchQueue.main.async { [weak self] in
            // Gentle, rhythmic pattern
            self?.selectionFeedback.selectionChanged()
        }
    }
    
    /// Error or negative feedback
    func playNegativeFeedback() {
        DispatchQueue.main.async { [weak self] in
            self?.notificationFeedback.notificationOccurred(.error)
        }
    }
    
    /// Success feedback
    func playSuccessFeedback() {
        DispatchQueue.main.async { [weak self] in
            self?.notificationFeedback.notificationOccurred(.success)
        }
    }
    
    /// Warning feedback
    func playWarningFeedback() {
        DispatchQueue.main.async { [weak self] in
            self?.notificationFeedback.notificationOccurred(.warning)
        }
    }
}

/// SwiftUI ViewModifier for easy haptic integration
struct EmotionalHapticFeedback: ViewModifier {
    let person: Person
    let action: HapticAction
    
    enum HapticAction {
        case tap
        case longPress
        case favorite
        case connection
        case celebration
        case specialMoment
    }
    
    func body(content: Content) -> some View {
        content
            .onTapGesture {
                handleHapticFeedback()
            }
    }
    
    private func handleHapticFeedback() {
        let haptics = EmotionalHapticsManager.shared
        
        switch action {
        case .tap:
            haptics.playConnectionPulse(for: person.interactionFrequency)
        case .longPress:
            haptics.playRelationshipFeedback(for: person.relationshipType)
        case .favorite:
            if person.isFavorite {
                haptics.playHeartbeatPattern()
            } else {
                haptics.playConnectionEstablished()
            }
        case .connection:
            haptics.playConnectionEstablished()
        case .celebration:
            haptics.playCelebrationPattern()
        case .specialMoment:
            haptics.playSpecialMomentPattern()
        }
    }
}

extension View {
    /// Add emotional haptic feedback to any view
    func emotionalHaptics(for person: Person, action: EmotionalHapticFeedback.HapticAction) -> some View {
        self.modifier(EmotionalHapticFeedback(person: person, action: action))
    }
}
