//
//  EmotionalContactsComponents.swift
//  Keeps
//
//  Created by <PERSON><PERSON> on 29/05/2025.
//

import SwiftUI
import SwiftUIX

/// Main emotional contacts area with different view modes
struct EmotionalContactsArea: View {
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    let viewMode: PeopleListView.ViewMode
    let celebrationTriggered: Bool
    
    var body: some View {
        GeometryReader { geometry in
            if !peopleManager.filteredPeople.isEmpty {
                switch viewMode {
                case .cards:
                    EmotionalCardsView(
                        people: peopleManager.filteredPeople,
                        peopleManager: peopleManager,
                        navigationCoordinator: navigationCoordinator,
                        celebrationTriggered: celebrationTriggered
                    )
                case .list:
                    EmotionalListView(
                        people: peopleManager.filteredPeople,
                        peopleManager: peopleManager
                    )
                case .grid:
                    EmotionalGridView(
                        people: peopleManager.filteredPeople,
                        peopleManager: peopleManager
                    )
                }
            } else {
                EmotionalEmptyState(
                    hasFilters: !peopleManager.searchText.isEmpty ||
                               peopleManager.selectedRelationshipType != nil ||
                               peopleManager.showFavoritesOnly
                )
            }
        }
    }
}

/// Revolutionary card-based view with emotional design
struct EmotionalCardsView: View {
    let people: [Person]
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    let celebrationTriggered: Bool
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 12) {
                ForEach(Array(people.enumerated()), id: \.element.id) { index, person in
                    ProfessionalPersonCard(
                        person: person,
                        index: index,
                        peopleManager: peopleManager,
                        navigationCoordinator: navigationCoordinator
                    )
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 100)
        }
    }
}

/// Individual emotional person card
struct EmotionalPersonCard: View {
    @ObservedObject var person: Person
    let index: Int
    @ObservedObject var peopleManager: PeopleManager
    let celebrationTriggered: Bool
    @State private var isPressed = false
    @State private var hasAppeared = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Enhanced avatar with emotional effects
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                person.relationshipType.color.opacity(0.8),
                                person.relationshipType.color
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                    .shadow(color: person.relationshipType.color.opacity(0.3), radius: 8, x: 0, y: 4)
                
                // Avatar content
                if person.avatarImageName.contains("person") {
                    Image(systemName: person.avatarImageName)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                } else {
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                }
                
                // Note: Removed online status indicator as online tracking was removed
            }
            // Add emotional animations
            .modifier(HeartbeatAnimation(isFavorite: person.isFavorite))
            .modifier(EmotionalStateIndicator(
                interactionFrequency: person.interactionFrequency,
                relationshipType: person.relationshipType
            ))
            
            // Person details
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(person.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    if person.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption)
                            .foregroundColor(.yellow)
                            .particleEffect(.favorite)
                    }
                    
                    Spacer()
                    
                    // Emotional connection indicator
                    EmotionalConnectionBadge(person: person)
                }
                
                if !person.contactInfo.isEmpty {
                    Text(person.contactInfo)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                // Relationship and interaction info
                HStack(spacing: 12) {
                    HStack(spacing: 4) {
                        Image(systemName: person.relationshipType.icon)
                            .font(.caption)
                            .foregroundColor(person.relationshipType.color)
                        
                        Text(person.relationshipType.rawValue)
                            .font(.caption)
                            .foregroundColor(person.relationshipType.color)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(person.relationshipType.color.opacity(0.1))
                    )
                    
                    Text(person.interactionFrequency.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
            
            // Quick actions
            VStack(spacing: 8) {
                Button(action: {
                    EmotionalHapticsManager.shared.playHeartbeatPattern()
                    withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                        peopleManager.toggleFavorite(for: person)
                    }
                }) {
                    Image(systemName: person.isFavorite ? "heart.fill" : "heart")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(person.isFavorite ? .red : .gray)
                }
                
                if !person.phone.isEmpty {
                    Button(action: {
                        EmotionalHapticsManager.shared.playConnectionEstablished()
                        let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                        if let url = URL(string: "tel:\(cleanPhone)") {
                            UIApplication.shared.open(url)
                        }
                    }) {
                        Image(systemName: "phone.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: person.relationshipType.color.opacity(0.1), radius: 10, x: 0, y: 4)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .opacity(hasAppeared ? 1 : 0)
        .offset(y: hasAppeared ? 0 : 50)
        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isPressed)
        .onAppear {
            let delay = Double(index) * 0.1
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.8)) {
                    hasAppeared = true
                }
            }
        }
        .onLongPressGesture(minimumDuration: 0) { pressing in
            isPressed = pressing
        } perform: {}
        .celebrationBurst(isTriggered: .constant(celebrationTriggered && person.isFavorite))
    }
}

/// Emotional connection strength badge
struct EmotionalConnectionBadge: View {
    @ObservedObject var person: Person
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<5, id: \.self) { index in
                Circle()
                    .fill(index < Int(Double(person.interactionFrequency.priority) / 5.0 * 5) ?
                          person.relationshipType.color : Color.gray.opacity(0.3))
                    .frame(width: 4, height: 4)
            }
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(
            Capsule()
                .fill(Color(.systemGray6))
        )
    }
}

/// Emotional list view
struct EmotionalListView: View {
    let people: [Person]
    @ObservedObject var peopleManager: PeopleManager
    
    var body: some View {
        List {
            ForEach(people) { person in
                EmotionalListRow(person: person, peopleManager: peopleManager)
                    .listRowBackground(Color.clear)
                    .listRowSeparator(.hidden)
            }
        }
        .listStyle(PlainListStyle())
    }
}

/// Emotional list row
struct EmotionalListRow: View {
    @ObservedObject var person: Person
    @ObservedObject var peopleManager: PeopleManager
    
    var body: some View {
        HStack(spacing: 12) {
            // Compact avatar
            Circle()
                .fill(person.relationshipType.color)
                .frame(width: 40, height: 40)
                .overlay(
                    Text(person.name.prefix(1).uppercased())
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                )
                .modifier(HeartbeatAnimation(isFavorite: person.isFavorite))
            
            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text(person.name)
                        .font(.body)
                        .fontWeight(.medium)
                    
                    if person.isFavorite {
                        Image(systemName: "star.fill")
                            .font(.caption2)
                            .foregroundColor(.yellow)
                    }
                    
                    Spacer()
                    
                    // Note: Removed online status indicator as online tracking was removed
                }
                
                Text(person.relationshipType.rawValue)
                    .font(.caption)
                    .foregroundColor(person.relationshipType.color)
            }
        }
        .padding(.vertical, 4)
        .onTapGesture {
            EmotionalHapticsManager.shared.playConnectionPulse(for: person.interactionFrequency)
            // Navigate to person detail - this would need to be implemented with proper navigation
        }
    }
}

/// Emotional grid view
struct EmotionalGridView: View {
    let people: [Person]
    @ObservedObject var peopleManager: PeopleManager
    
    let columns = Array(repeating: GridItem(.flexible(), spacing: 16), count: 2)
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: 16) {
                ForEach(people) { person in
                    EmotionalGridCard(person: person, peopleManager: peopleManager)
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 100)
        }
    }
}

/// Emotional grid card
struct EmotionalGridCard: View {
    @ObservedObject var person: Person
    @ObservedObject var peopleManager: PeopleManager
    
    var body: some View {
        VStack(spacing: 12) {
            // Avatar
            Circle()
                .fill(person.relationshipType.color)
                .frame(width: 50, height: 50)
                .overlay(
                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                )
                .modifier(HeartbeatAnimation(isFavorite: person.isFavorite))
                // Note: Removed BreathingAnimation as online tracking was removed
            
            VStack(spacing: 4) {
                Text(person.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(person.relationshipType.rawValue)
                    .font(.caption)
                    .foregroundColor(person.relationshipType.color)
                
                EmotionalConnectionBadge(person: person)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .shadow(color: person.relationshipType.color.opacity(0.1), radius: 6)
        )
        .onTapGesture {
            EmotionalHapticsManager.shared.playConnectionPulse(for: person.interactionFrequency)
            // Navigate to person detail - this would need to be implemented with proper navigation
        }
    }
}

/// Enhanced professional person card with swipe gestures, contextual menus, and accessibility
struct ProfessionalPersonCard: View {
    @ObservedObject var person: Person
    let index: Int
    @ObservedObject var peopleManager: PeopleManager
    @ObservedObject var navigationCoordinator: NavigationCoordinator
    @State private var dragOffset: CGFloat = 0
    @State private var isPressed = false
    @State private var showingContextMenu = false
    @State private var hasAppeared = false
    @State private var showingQuickActions = false

    // Performance and accessibility
    @ObservedObject private var accessibilityManager = AccessibilityManager.shared
    @ObservedObject private var performanceMonitor = PerformanceMonitor.shared

    private let swipeThreshold: CGFloat = 80
    private let maxSwipeDistance: CGFloat = 120

    var body: some View {
        ZStack {
            // Background swipe actions
            HStack {
                Spacer()

                // Right swipe actions (revealed when swiping left)
                HStack(spacing: 0) {
                    // Call action
                    if !person.phone.isEmpty {
                        SwipeActionButton(
                            icon: "phone.fill",
                            color: .green,
                            action: {
                                callPerson()
                            }
                        )
                    }

                    // Message action
                    if !person.phone.isEmpty {
                        SwipeActionButton(
                            icon: "message.fill",
                            color: .blue,
                            action: {
                                messagePerson()
                            }
                        )
                    }

                    // Email action
                    if !person.email.isEmpty {
                        SwipeActionButton(
                            icon: "envelope.fill",
                            color: .orange,
                            action: {
                                emailPerson()
                            }
                        )
                    }
                }
                .opacity(dragOffset < -20 ? 1 : 0)
                .animation(.easeInOut(duration: 0.2), value: dragOffset)
            }

            // Main card content
            HStack(spacing: 16) {
                // Enhanced avatar with smart suggestions
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    person.relationshipType.color,
                                    person.relationshipType.color.opacity(0.8)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 48, height: 48)
                        .shadow(color: person.relationshipType.color.opacity(0.3), radius: 4, x: 0, y: 2)

                    Text(person.name.prefix(2).uppercased())
                        .font(.system(size: 16, weight: .medium, design: .rounded))
                        .foregroundColor(.white)

                    // Note: Removed online indicator as online tracking was removed

                    // Smart suggestion indicator
                    if shouldShowSuggestion {
                        Circle()
                            .fill(.blue)
                            .frame(width: 8, height: 8)
                            .offset(x: -16, y: 16)
                            .animation(.easeInOut(duration: 1).repeatForever(autoreverses: true), value: hasAppeared)
                    }
                }

                // Enhanced person info with smart insights
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(person.name)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)

                        if person.isFavorite {
                            Image(systemName: "star.fill")
                                .font(.system(size: 12))
                                .foregroundColor(.orange)
                        }

                        // Smart suggestion badge
                        if shouldShowSuggestion {
                            Text("Follow up")
                                .font(.system(size: 10, weight: .medium))
                                .foregroundColor(.blue)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    Capsule()
                                        .fill(.blue.opacity(0.1))
                                )
                        }

                        Spacer()

                        // Note: Removed last contact indicator as lastSeen tracking was removed
                    }

                    Text(person.role)
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .lineLimit(1)

                    HStack(spacing: 8) {
                        // Enhanced relationship badge
                        HStack(spacing: 4) {
                            Image(systemName: person.relationshipType.icon)
                                .font(.system(size: 10))
                                .foregroundColor(person.relationshipType.color)

                            Text(person.relationshipType.rawValue)
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(person.relationshipType.color)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(person.relationshipType.color.opacity(0.1))
                        )

                        // Connection strength indicator
                        ConnectionStrengthIndicator(person: person)

                        Spacer()
                    }
                }

                // Enhanced action button with context menu
                Button(action: {
                    // Haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        peopleManager.toggleFavorite(for: person)
                    }
                }) {
                    Image(systemName: person.isFavorite ? "heart.fill" : "heart")
                        .font(.system(size: 16))
                        .foregroundColor(person.isFavorite ? .red : .gray)
                        .scaleEffect(isPressed ? 0.9 : 1.0)
                }
                .contextMenu {
                    ContextMenuContent(person: person, peopleManager: peopleManager, navigationCoordinator: navigationCoordinator)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .offset(x: dragOffset)
            .opacity(hasAppeared ? 1 : 0)
            .offset(y: hasAppeared ? 0 : 20)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isPressed)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: dragOffset)
            .gesture(
                // Combine tap and drag gestures with proper priority
                SimultaneousGesture(
                    TapGesture()
                        .onEnded { _ in
                            // Enhanced haptic feedback based on accessibility settings
                            if accessibilityManager.isVoiceOverEnabled {
                                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                impactFeedback.impactOccurred()
                            } else {
                                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                impactFeedback.impactOccurred()
                            }

                            peopleManager.selectedPerson = person
                        },
                    DragGesture(minimumDistance: 20) // Require minimum distance to prevent accidental drags
                        .onChanged { value in
                            // Only allow left swipe (negative translation) and require minimum distance
                            if value.translation.width < -20 {
                                dragOffset = max(value.translation.width, -maxSwipeDistance)
                            }
                        }
                        .onEnded { value in
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                if dragOffset < -swipeThreshold {
                                    // Show quick actions briefly then reset
                                    showingQuickActions = true
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                        dragOffset = 0
                                        showingQuickActions = false
                                    }
                                } else {
                                    dragOffset = 0
                                }
                            }
                        }
                )
            )
            .onLongPressGesture(minimumDuration: 0) { pressing in
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    isPressed = pressing
                }
            } perform: {}
            .onAppear {
                // Staggered animation with accessibility consideration
                let delay = accessibilityManager.isReduceMotionEnabled ? 0.0 : Double(index) * 0.05
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    withAnimation(accessibilityManager.springAnimation(response: 0.8, dampingFraction: 0.8)) {
                        hasAppeared = true
                    }
                }
            }
            // Enhanced accessibility support
            .accessibilityEnhanced(
                label: "\(person.name), \(person.role) at \(person.company)",
                hint: "Double tap to view details, swipe left for quick actions",
                value: "\(person.isFavorite ? "Favorite" : "Not favorite")",
                traits: [.isButton],
                defaultAction: {
                    peopleManager.selectedPerson = person
                }
            )
            .accessibilityAction(.magicTap) {
                // Quick call action for VoiceOver users
                if !person.phone.isEmpty {
                    callPerson()
                }
            }
            .dynamicTypeSupport()
            .reducedMotionSupport(
                value: hasAppeared,
                normalAnimation: .spring(response: 0.8, dampingFraction: 0.8),
                reducedAnimation: .easeInOut(duration: 0.1)
            )
        }
    }

    // MARK: - Smart Suggestions
    private var shouldShowSuggestion: Bool {
        // Note: Removed lastSeen tracking, using interaction frequency as fallback
        return person.interactionFrequency == .rarely || person.interactionFrequency == .never
    }

    // MARK: - Actions
    private func callPerson() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        if let url = URL(string: "tel:\(cleanPhone)") {
            UIApplication.shared.open(url)
        }

        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            dragOffset = 0
        }
    }

    private func messagePerson() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
        if let url = URL(string: "sms:\(cleanPhone)") {
            UIApplication.shared.open(url)
        }

        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            dragOffset = 0
        }
    }

    private func emailPerson() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        if let url = URL(string: "mailto:\(person.email)") {
            UIApplication.shared.open(url)
        }

        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            dragOffset = 0
        }
    }
}

/// Emotional empty state
struct EmotionalEmptyState: View {
    let hasFilters: Bool

    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: hasFilters ? "heart.slash" : "heart.circle")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text(hasFilters ? "No Connections Found" : "No Connections Yet")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text(hasFilters ? "Try adjusting your filters to find your connections" : "Add your first connection to start building your network")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(40)
    }
}

// MARK: - Enhanced Navigation Components

/// Swipe action button for contact cards
struct SwipeActionButton: View {
    let icon: String
    let color: Color
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: {
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()

            action()
        }) {
            Image(systemName: icon)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(color)
                        .shadow(color: color.opacity(0.3), radius: 4, x: 0, y: 2)
                )
                .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0) { pressing in
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                isPressed = pressing
            }
        } perform: {}
    }
}

/// Context menu content for contact cards
struct ContextMenuContent: View {
    @ObservedObject var person: Person
    @ObservedObject var peopleManager: PeopleManager
    var navigationCoordinator: NavigationCoordinator?

    var body: some View {
        Group {
            // Quick actions section
            if !person.phone.isEmpty {
                Button(action: {
                    let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                    if let url = URL(string: "tel:\(cleanPhone)") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Label("Call", systemImage: "phone.fill")
                }

                Button(action: {
                    let cleanPhone = person.phone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()
                    if let url = URL(string: "sms:\(cleanPhone)") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Label("Message", systemImage: "message.fill")
                }
            }

            if !person.email.isEmpty {
                Button(action: {
                    if let url = URL(string: "mailto:\(person.email)") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    Label("Email", systemImage: "envelope.fill")
                }
            }

            Divider()

            // Cross-section navigation
            if let navigationCoordinator = navigationCoordinator {
                Button(action: {
                    navigationCoordinator.navigateToRelated(.personTeams(person.id))
                }) {
                    Label("View Teams", systemImage: "person.3")
                }

                Button(action: {
                    navigationCoordinator.navigateToRelated(.personTimeline(person.id))
                }) {
                    Label("View in Timeline", systemImage: "timeline.selection")
                }

                Divider()
            }

            // Management actions
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    peopleManager.toggleFavorite(for: person)
                }
            }) {
                Label(
                    person.isFavorite ? "Remove from Favorites" : "Add to Favorites",
                    systemImage: person.isFavorite ? "star.slash" : "star.fill"
                )
            }

            Button(action: {
                // Open edit view (would need to be implemented)
            }) {
                Label("Edit Contact", systemImage: "pencil")
            }

            Divider()

            // Smart suggestions
            if shouldShowFollowUpSuggestion {
                Button(action: {
                    // Create follow-up reminder
                }) {
                    Label("Set Follow-up Reminder", systemImage: "bell.badge")
                }
            }

            Button(action: {
                // Add note
            }) {
                Label("Add Note", systemImage: "note.text.badge.plus")
            }
        }
    }

    private var shouldShowFollowUpSuggestion: Bool {
        // Note: Removed lastSeen tracking, using interaction frequency as fallback
        return person.interactionFrequency == .rarely || person.interactionFrequency == .never
    }
}

/// Connection strength indicator for contact cards
struct ConnectionStrengthIndicator: View {
    @ObservedObject var person: Person

    private var strengthLevel: Int {
        switch person.interactionFrequency {
        case .never: return 0
        case .rarely: return 1
        case .monthly: return 2
        case .weekly: return 3
        case .daily: return 4
        }
    }

    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<4, id: \.self) { index in
                RoundedRectangle(cornerRadius: 1)
                    .fill(index < strengthLevel ? person.relationshipType.color : Color.gray.opacity(0.3))
                    .frame(width: 3, height: index < strengthLevel ? 8 : 4)
                    .animation(.easeInOut(duration: 0.3).delay(Double(index) * 0.1), value: strengthLevel)
            }
        }
    }
}
